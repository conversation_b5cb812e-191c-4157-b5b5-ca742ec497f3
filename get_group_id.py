#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取Telegram群组ID的工具脚本
"""

import requests
import json
from config import TELEGRAM_BOT_TOKEN

def get_group_id():
    """获取bot接收到的所有群组信息"""
    
    print("🔍 获取Telegram群组ID")
    print("=" * 30)
    
    # Telegram Bot API URL
    api_url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/getUpdates"
    
    try:
        print("📡 正在获取bot更新信息...")
        response = requests.get(api_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('ok'):
                updates = data.get('result', [])
                print(f"✅ 获取到 {len(updates)} 条更新记录")
                
                # 提取群组信息
                groups = {}
                
                for update in updates:
                    message = update.get('message', {})
                    chat = message.get('chat', {})
                    
                    if chat.get('type') in ['group', 'supergroup']:
                        chat_id = chat.get('id')
                        chat_title = chat.get('title', '未知群组')
                        chat_type = chat.get('type')
                        
                        if chat_id:
                            groups[chat_id] = {
                                'title': chat_title,
                                'type': chat_type,
                                'username': chat.get('username', ''),
                                'description': chat.get('description', '')
                            }
                
                if groups:
                    print("\n📋 发现的群组:")
                    print("-" * 50)
                    
                    for chat_id, info in groups.items():
                        print(f"群组名称: {info['title']}")
                        print(f"群组ID: {chat_id}")
                        print(f"类型: {info['type']}")
                        if info['username']:
                            print(f"用户名: @{info['username']}")
                        print("-" * 30)
                    
                    # 检查是否有Pythia_DAO相关群组
                    pythia_groups = []
                    for chat_id, info in groups.items():
                        title_lower = info['title'].lower()
                        username_lower = info.get('username', '').lower()
                        
                        if 'pythia' in title_lower or 'dao' in title_lower or 'pythia_dao' in username_lower:
                            pythia_groups.append((chat_id, info))
                    
                    if pythia_groups:
                        print("\n🎯 Pythia相关群组:")
                        print("=" * 40)
                        for chat_id, info in pythia_groups:
                            print(f"✅ {info['title']}")
                            print(f"   ID: {chat_id}")
                            if info['username']:
                                print(f"   链接: https://t.me/{info['username']}")
                            print()
                    
                    return groups
                else:
                    print("⚠️ 未发现任何群组")
                    print("💡 请确保:")
                    print("   1. Bot已被添加到目标群组")
                    print("   2. 在群组中发送过消息给bot")
                    print("   3. Bot有读取消息的权限")
                    
            else:
                print(f"❌ API返回错误: {data.get('description', '未知错误')}")
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求失败: {e}")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
    
    return None

def add_group_to_config(chat_id, group_name):
    """将群组ID添加到配置文件"""
    
    print(f"\n🔧 添加群组到配置文件")
    print(f"群组: {group_name}")
    print(f"ID: {chat_id}")
    
    try:
        # 读取当前配置
        with open('config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找TELEGRAM_REPORT_CHATS配置
        if 'TELEGRAM_REPORT_CHATS' in content:
            # 在现有配置中添加新群组ID
            lines = content.split('\n')
            new_lines = []
            in_report_chats = False
            
            for line in lines:
                if 'TELEGRAM_REPORT_CHATS = [' in line:
                    in_report_chats = True
                    new_lines.append(line)
                    new_lines.append(f'    "{chat_id}",   # {group_name}')
                elif in_report_chats and line.strip() == ']':
                    in_report_chats = False
                    new_lines.append(line)
                else:
                    new_lines.append(line)
            
            # 写回文件
            with open('config.py', 'w', encoding='utf-8') as f:
                f.write('\n'.join(new_lines))
            
            print("✅ 配置文件已更新")
            
        else:
            print("⚠️ 未找到TELEGRAM_REPORT_CHATS配置")
            
    except Exception as e:
        print(f"❌ 更新配置文件失败: {e}")

def main():
    """主函数"""
    
    print("🤖 Telegram群组ID获取工具")
    print("=" * 40)
    print()
    
    # 获取群组信息
    groups = get_group_id()
    
    if groups:
        print("\n" + "=" * 50)
        print("📝 使用说明:")
        print("1. 如果没有看到目标群组，请:")
        print("   - 将bot添加到群组: @pythia_is_ai_bot")
        print("   - 在群组中发送任意消息")
        print("   - 重新运行此脚本")
        print()
        print("2. 要添加群组到配置文件:")
        print("   - 复制群组ID")
        print("   - 手动编辑config.py文件")
        print("   - 或使用add_group_to_config函数")
        print()
        print("3. Pythia_DAO群组链接:")
        print("   https://t.me/Pythia_DAO")
        
    else:
        print("\n💡 获取群组ID的步骤:")
        print("1. 打开群组: https://t.me/Pythia_DAO")
        print("2. 添加bot: @pythia_is_ai_bot")
        print("3. 在群组中发送: /start 或任意消息")
        print("4. 重新运行此脚本")

if __name__ == "__main__":
    main()
