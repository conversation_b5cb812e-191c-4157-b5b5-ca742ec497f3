#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PYTHIA专属知识库管理系统
基于JSON的智能知识库，支持查询、更新、搜索等功能
"""

import json
import os
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import difflib


class PythiaKnowledgeManager:
    """PYTHIA专属知识库管理器"""
    
    def __init__(self, knowledge_file: str = "pythia_knowledge_base.json"):
        self.knowledge_file = Path(knowledge_file)
        self.knowledge_base = self._load_knowledge_base()
        
    def _load_knowledge_base(self) -> Dict[str, Any]:
        """加载知识库"""
        if self.knowledge_file.exists():
            try:
                with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError) as e:
                print(f"⚠️ 无法加载知识库文件: {e}")
                return {}
        else:
            print(f"⚠️ 知识库文件不存在: {self.knowledge_file}")
            return {}
    
    def _save_knowledge_base(self) -> bool:
        """保存知识库"""
        try:
            # 更新最后修改时间
            if "metadata" in self.knowledge_base:
                self.knowledge_base["metadata"]["last_updated"] = datetime.now().strftime("%Y-%m-%d")
            
            with open(self.knowledge_file, 'w', encoding='utf-8') as f:
                json.dump(self.knowledge_base, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"❌ 保存知识库失败: {e}")
            return False
    
    def search_knowledge(self, query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
        """智能搜索知识库内容"""
        results = []
        query_lower = query.lower()
        
        def search_recursive(data: Any, path: str = "", parent_key: str = ""):
            """递归搜索数据结构"""
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 检查键名匹配
                    if query_lower in key.lower():
                        results.append({
                            "type": "key_match",
                            "path": current_path,
                            "key": key,
                            "value": value,
                            "context": parent_key
                        })
                    
                    # 递归搜索值
                    search_recursive(value, current_path, key)
                    
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]"
                    search_recursive(item, current_path, parent_key)
                    
            elif isinstance(data, str):
                # 检查字符串内容匹配
                if query_lower in data.lower():
                    results.append({
                        "type": "content_match",
                        "path": path,
                        "content": data,
                        "context": parent_key
                    })
        
        # 如果指定了类别，只在该类别中搜索
        if category and category in self.knowledge_base:
            search_recursive(self.knowledge_base[category], category)
        else:
            search_recursive(self.knowledge_base)
        
        return results
    
    def get_by_path(self, path: str) -> Optional[Any]:
        """根据路径获取知识库内容"""
        try:
            keys = path.split('.')
            current = self.knowledge_base
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            
            return current
        except Exception:
            return None
    
    def update_by_path(self, path: str, value: Any) -> bool:
        """根据路径更新知识库内容"""
        try:
            keys = path.split('.')
            current = self.knowledge_base
            
            # 导航到父级
            for key in keys[:-1]:
                if isinstance(current, dict):
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                else:
                    return False
            
            # 设置最终值
            if isinstance(current, dict):
                current[keys[-1]] = value
                return self._save_knowledge_base()
            
            return False
        except Exception as e:
            print(f"❌ 更新失败: {e}")
            return False
    
    def get_company_info(self) -> Dict[str, Any]:
        """获取公司信息"""
        return self.get_by_path("company_info") or {}
    
    def get_leadership_team(self) -> Dict[str, Any]:
        """获取领导团队信息"""
        return self.get_by_path("leadership_team") or {}
    
    def get_scientific_core(self) -> Dict[str, Any]:
        """获取科学核心信息"""
        return self.get_by_path("scientific_core") or {}
    
    def get_token_economics(self) -> Dict[str, Any]:
        """获取代币经济学信息"""
        return self.get_by_path("token_economics") or {}
    
    def get_commercial_products(self) -> Dict[str, Any]:
        """获取商业产品信息"""
        return self.get_by_path("commercial_products") or {}
    
    def get_investment_analysis(self) -> Dict[str, Any]:
        """获取投资分析信息"""
        return self.get_by_path("investment_analysis") or {}
    
    def get_ethical_framework(self) -> Dict[str, Any]:
        """获取伦理框架信息"""
        return self.get_by_path("ethical_framework") or {}
    
    def find_similar_content(self, text: str, threshold: float = 0.6) -> List[Dict[str, Any]]:
        """查找相似内容"""
        results = []
        
        def extract_strings(data: Any, path: str = ""):
            """提取所有字符串内容"""
            strings = []
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    strings.extend(extract_strings(value, current_path))
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]"
                    strings.extend(extract_strings(item, current_path))
            elif isinstance(data, str) and len(data) > 10:  # 只考虑较长的字符串
                strings.append((path, data))
            return strings
        
        all_strings = extract_strings(self.knowledge_base)
        
        for path, content in all_strings:
            similarity = difflib.SequenceMatcher(None, text.lower(), content.lower()).ratio()
            if similarity >= threshold:
                results.append({
                    "path": path,
                    "content": content,
                    "similarity": similarity
                })
        
        # 按相似度排序
        results.sort(key=lambda x: x["similarity"], reverse=True)
        return results
    
    def get_summary(self) -> Dict[str, Any]:
        """获取知识库摘要"""
        summary = {
            "metadata": self.get_by_path("metadata"),
            "project_overview": self.get_by_path("project_overview"),
            "key_metrics": {
                "company_founded": self.get_by_path("company_info.neiry_lab.founded"),
                "token_symbol": self.get_by_path("token_economics.basic_info.symbol"),
                "current_market_cap": self.get_by_path("token_economics.market_performance.current_market_cap_range"),
                "leadership_count": len(self.get_leadership_team()),
                "product_count": len(self.get_commercial_products())
            }
        }
        return summary
    
    def validate_knowledge_base(self) -> Dict[str, Any]:
        """验证知识库完整性"""
        required_sections = [
            "metadata", "project_overview", "company_info", 
            "leadership_team", "scientific_core", "token_economics"
        ]
        
        validation_result = {
            "is_valid": True,
            "missing_sections": [],
            "warnings": [],
            "statistics": {}
        }
        
        # 检查必需部分
        for section in required_sections:
            if section not in self.knowledge_base:
                validation_result["missing_sections"].append(section)
                validation_result["is_valid"] = False
        
        # 统计信息
        validation_result["statistics"] = {
            "total_sections": len(self.knowledge_base),
            "total_size_kb": len(json.dumps(self.knowledge_base)) / 1024
        }
        
        return validation_result


# 便捷函数
def create_knowledge_manager() -> PythiaKnowledgeManager:
    """创建知识库管理器实例"""
    return PythiaKnowledgeManager()


def quick_search(query: str, category: Optional[str] = None) -> List[Dict[str, Any]]:
    """快速搜索"""
    km = create_knowledge_manager()
    return km.search_knowledge(query, category)


def get_project_summary() -> Dict[str, Any]:
    """获取项目摘要"""
    km = create_knowledge_manager()
    return km.get_summary()


if __name__ == "__main__":
    # 测试代码
    km = create_knowledge_manager()
    
    print("🔍 PYTHIA知识库管理系统测试")
    print("=" * 50)
    
    # 验证知识库
    validation = km.validate_knowledge_base()
    print(f"✅ 知识库验证: {'通过' if validation['is_valid'] else '失败'}")
    
    # 获取摘要
    summary = km.get_summary()
    print(f"📊 项目名称: {summary['project_overview']['name']}")
    print(f"📊 公司成立: {summary['key_metrics']['company_founded']}")
    print(f"📊 代币符号: {summary['key_metrics']['token_symbol']}")
    
    # 搜索测试
    search_results = km.search_knowledge("Mikhail Lebedev")
    print(f"🔍 搜索'Mikhail Lebedev'找到 {len(search_results)} 个结果")
