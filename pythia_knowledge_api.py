#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PYTHIA知识库API接口
提供智能查询、动态更新、数据分析等高级功能
"""

import json
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pythia_knowledge_manager import PythiaKnowledgeManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PythiaKnowledgeAPI:
    """PYTHIA知识库高级API接口"""
    
    def __init__(self):
        self.km = PythiaKnowledgeManager()
        self.query_cache = {}
        self.cache_ttl = timedelta(minutes=30)
        
    def intelligent_query(self, question: str, context: str = "") -> Dict[str, Any]:
        """智能查询 - 根据问题自动匹配最相关的知识"""
        
        # 缓存检查
        cache_key = f"{question}:{context}"
        if self._is_cache_valid(cache_key):
            return self.query_cache[cache_key]["result"]
        
        # 问题分类
        question_type = self._classify_question(question)
        
        # 根据问题类型查询相关信息
        result = {
            "question": question,
            "question_type": question_type,
            "relevant_info": [],
            "confidence": 0.0,
            "sources": [],
            "suggestions": []
        }
        
        # 执行查询
        if question_type == "company":
            result["relevant_info"] = self._get_company_related_info(question)
        elif question_type == "technology":
            result["relevant_info"] = self._get_technology_related_info(question)
        elif question_type == "token":
            result["relevant_info"] = self._get_token_related_info(question)
        elif question_type == "investment":
            result["relevant_info"] = self._get_investment_related_info(question)
        elif question_type == "ethics":
            result["relevant_info"] = self._get_ethics_related_info(question)
        else:
            # 通用搜索
            search_results = self.km.search_knowledge(question)
            result["relevant_info"] = search_results[:5]  # 限制结果数量
        
        # 计算置信度
        result["confidence"] = self._calculate_confidence(result["relevant_info"])
        
        # 生成建议
        result["suggestions"] = self._generate_suggestions(question, question_type)
        
        # 缓存结果
        self._cache_result(cache_key, result)
        
        return result
    
    def _classify_question(self, question: str) -> str:
        """问题分类"""
        question_lower = question.lower()
        
        # 定义关键词映射
        keywords_map = {
            "company": ["公司", "neiry", "团队", "创始人", "ceo", "领导", "背景"],
            "technology": ["技术", "实验", "大鼠", "ai", "人工智能", "脑机", "神经", "算法"],
            "token": ["代币", "pythia", "价格", "市值", "交易", "经济学", "供应"],
            "investment": ["投资", "风险", "收益", "分析", "机会", "前景", "估值"],
            "ethics": ["伦理", "道德", "安全", "隐私", "责任", "风险", "合规"]
        }
        
        # 计算每个类别的匹配分数
        scores = {}
        for category, keywords in keywords_map.items():
            score = sum(1 for keyword in keywords if keyword in question_lower)
            scores[category] = score
        
        # 返回得分最高的类别
        if max(scores.values()) > 0:
            return max(scores, key=scores.get)
        else:
            return "general"
    
    def _get_company_related_info(self, question: str) -> List[Dict[str, Any]]:
        """获取公司相关信息"""
        info = []
        
        # 公司基本信息
        company_info = self.km.get_company_info()
        if company_info:
            info.append({
                "type": "company_overview",
                "title": "Neiry Lab公司概况",
                "content": company_info.get("neiry_lab", {}),
                "relevance": 0.9
            })
        
        # 领导团队
        leadership = self.km.get_leadership_team()
        if leadership:
            info.append({
                "type": "leadership",
                "title": "核心领导团队",
                "content": leadership,
                "relevance": 0.8
            })
        
        return info
    
    def _get_technology_related_info(self, question: str) -> List[Dict[str, Any]]:
        """获取技术相关信息"""
        info = []
        
        # 科学核心
        scientific_core = self.km.get_scientific_core()
        if scientific_core:
            info.append({
                "type": "scientific_core",
                "title": "核心科学技术",
                "content": scientific_core,
                "relevance": 0.95
            })
        
        # 商业产品
        products = self.km.get_commercial_products()
        if products:
            info.append({
                "type": "products",
                "title": "商业产品线",
                "content": products,
                "relevance": 0.7
            })
        
        return info
    
    def _get_token_related_info(self, question: str) -> List[Dict[str, Any]]:
        """获取代币相关信息"""
        info = []
        
        token_economics = self.km.get_token_economics()
        if token_economics:
            info.append({
                "type": "token_economics",
                "title": "PYTHIA代币经济学",
                "content": token_economics,
                "relevance": 0.95
            })
        
        return info
    
    def _get_investment_related_info(self, question: str) -> List[Dict[str, Any]]:
        """获取投资相关信息"""
        info = []
        
        investment_analysis = self.km.get_investment_analysis()
        if investment_analysis:
            info.append({
                "type": "investment_analysis",
                "title": "投资分析",
                "content": investment_analysis,
                "relevance": 0.9
            })
        
        return info
    
    def _get_ethics_related_info(self, question: str) -> List[Dict[str, Any]]:
        """获取伦理相关信息"""
        info = []
        
        ethical_framework = self.km.get_ethical_framework()
        if ethical_framework:
            info.append({
                "type": "ethical_framework",
                "title": "伦理框架",
                "content": ethical_framework,
                "relevance": 0.9
            })
        
        return info
    
    def _calculate_confidence(self, relevant_info: List[Dict[str, Any]]) -> float:
        """计算查询结果的置信度"""
        if not relevant_info:
            return 0.0
        
        # 基于相关性分数计算平均置信度
        relevance_scores = [item.get("relevance", 0.5) for item in relevant_info]
        return sum(relevance_scores) / len(relevance_scores)
    
    def _generate_suggestions(self, question: str, question_type: str) -> List[str]:
        """生成相关建议"""
        suggestions = []
        
        if question_type == "company":
            suggestions.extend([
                "了解Neiry Lab的融资历史",
                "查看核心团队的学术背景",
                "探索公司的商业产品线"
            ])
        elif question_type == "technology":
            suggestions.extend([
                "深入了解脑机接口技术原理",
                "查看技术发展路线图",
                "了解实验的伦理考量"
            ])
        elif question_type == "token":
            suggestions.extend([
                "分析代币的市场表现",
                "了解代币的实用价值",
                "查看交易所上市情况"
            ])
        
        return suggestions[:3]  # 限制建议数量
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.query_cache:
            return False
        
        cache_time = self.query_cache[cache_key]["timestamp"]
        return datetime.now() - cache_time < self.cache_ttl
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """缓存查询结果"""
        self.query_cache[cache_key] = {
            "result": result,
            "timestamp": datetime.now()
        }
    
    def get_knowledge_stats(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        validation = self.km.validate_knowledge_base()
        summary = self.km.get_summary()
        
        return {
            "knowledge_base_status": "healthy" if validation["is_valid"] else "needs_attention",
            "total_sections": validation["statistics"]["total_sections"],
            "size_kb": round(validation["statistics"]["total_size_kb"], 2),
            "last_updated": summary["metadata"]["last_updated"],
            "cache_entries": len(self.query_cache),
            "missing_sections": validation["missing_sections"]
        }
    
    def update_market_data(self, market_data: Dict[str, Any]) -> bool:
        """更新市场数据"""
        try:
            # 更新代币市场表现数据
            current_data = self.km.get_by_path("token_economics.market_performance") or {}
            
            # 合并新数据
            updated_data = {**current_data, **market_data}
            updated_data["last_updated"] = datetime.now().isoformat()
            
            # 保存更新
            success = self.km.update_by_path("token_economics.market_performance", updated_data)
            
            if success:
                logger.info("市场数据更新成功")
                # 清除相关缓存
                self._clear_cache_by_pattern("token")
            
            return success
            
        except Exception as e:
            logger.error(f"更新市场数据失败: {e}")
            return False
    
    def _clear_cache_by_pattern(self, pattern: str):
        """根据模式清除缓存"""
        keys_to_remove = [key for key in self.query_cache.keys() if pattern in key.lower()]
        for key in keys_to_remove:
            del self.query_cache[key]
    
    def export_knowledge_summary(self, format: str = "json") -> str:
        """导出知识库摘要"""
        summary = self.km.get_summary()
        stats = self.get_knowledge_stats()
        
        export_data = {
            "export_timestamp": datetime.now().isoformat(),
            "knowledge_summary": summary,
            "statistics": stats
        }
        
        if format.lower() == "json":
            return json.dumps(export_data, ensure_ascii=False, indent=2)
        else:
            # 可以扩展支持其他格式
            return str(export_data)


# 便捷函数
def create_knowledge_api() -> PythiaKnowledgeAPI:
    """创建知识库API实例"""
    return PythiaKnowledgeAPI()


def quick_intelligent_query(question: str) -> Dict[str, Any]:
    """快速智能查询"""
    api = create_knowledge_api()
    return api.intelligent_query(question)


if __name__ == "__main__":
    # 测试代码
    api = create_knowledge_api()
    
    print("🔍 PYTHIA知识库API测试")
    print("=" * 50)
    
    # 测试智能查询
    test_questions = [
        "Neiry Lab公司的背景如何？",
        "PYTHIA的技术原理是什么？",
        "代币的市场表现怎么样？",
        "投资风险有哪些？"
    ]
    
    for question in test_questions:
        print(f"\n❓ 问题: {question}")
        result = api.intelligent_query(question)
        print(f"📊 问题类型: {result['question_type']}")
        print(f"🎯 置信度: {result['confidence']:.2f}")
        print(f"📝 相关信息数量: {len(result['relevant_info'])}")
    
    # 显示统计信息
    stats = api.get_knowledge_stats()
    print(f"\n📈 知识库统计:")
    print(f"  状态: {stats['knowledge_base_status']}")
    print(f"  大小: {stats['size_kb']} KB")
    print(f"  缓存条目: {stats['cache_entries']}")
